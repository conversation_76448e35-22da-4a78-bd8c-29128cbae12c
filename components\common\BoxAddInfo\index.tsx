import {
    <PERSON><PERSON>,
    <PERSON>,
    CardB<PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    Col,
    Label,
    Row,
} from 'reactstrap';
import { useState } from 'react';
import DropdownActionMenu from '../DropdownActionMenu';

interface BoxAddInfoItem {
    title?: string;
    infoAdd?: string;
    data: [
        {
            label: string;
            value: string;
            boxColor?: boolean;
        },
    ];
    onViewQuick?: () => void;
    onViewDetail?: () => void;
    onDelete?: () => void;
}

interface BoxAddInfoProps {
    title: string;
    length: number;
    onAdd?: () => void;
    content: BoxAddInfoItem[];
}

const BoxAddInfo = ({
    title,
    length,
    content = [],
    onAdd,
}: BoxAddInfoProps) => {
    const [showAll, setShowAll] = useState(false);

    // Hiển thị chỉ 1 phần tử đầu tiên nếu chưa bấm "Xem thêm"
    const displayedContent = showAll ? content : content.slice(0, 1);
    const hasMoreItems = content.length > 1;

    return (
        <Col md={12}>
            <Card className='mb-3'>
                <CardHeader>
                    <Row>
                        <Col md={9}>
                            <h5 className='mb-0'>
                                {title} ({length})
                            </h5>
                        </Col>
                        <Col md={3}>
                            <Button
                                color='success'
                                size='sm'
                                onClick={onAdd}
                                style={{
                                    backgroundColor: '#ffffff',
                                    borderColor: '#0ab39c',
                                    color: '#0ab39c',
                                }}
                            >
                                <i className='ri-add-line align-middle'></i>
                                Thêm
                            </Button>
                        </Col>
                    </Row>
                </CardHeader>
                <CardBody>
                    {displayedContent.map((item, index) => (
                        <div
                            key={index}
                            className='mb-3'
                            style={{
                                borderBottom: '1px solid #e9ebec',
                            }}
                        >
                            <div className='p-3 rounded'>
                                <div className='d-flex align-items-center justify-content-between mb-2'>
                                    <h6 className='mb-0'>{item.title}</h6>
                                    <DropdownActionMenu
                                        actions={[
                                            {
                                                icon: 'ri-eye-line',
                                                label: 'Xem nhanh',
                                                onClick:
                                                    item.onViewQuick ||
                                                    (() => {}),
                                            },
                                            {
                                                icon: 'ri-eye-fill',
                                                label: 'Xem chi tiết',
                                                onClick:
                                                    item.onViewDetail ||
                                                    (() => {}),
                                            },
                                            {
                                                icon: 'ri-delete-bin-line',
                                                label: 'Xóa',
                                                onClick:
                                                    item.onDelete || (() => {}),
                                                className: 'text-danger',
                                            },
                                        ]}
                                        toggleIcon='ri-more-2-fill'
                                    />
                                </div>
                                {item.infoAdd && (
                                    <small className='text-muted d-block mb-2'>
                                        {item.infoAdd}
                                    </small>
                                )}
                                {item.data.map((dataItem, dataIndex) => (
                                    <div
                                        key={dataIndex}
                                        className='d-flex align-items-center gap-2 mb-1'
                                    >
                                        <Label
                                            className='mb-0'
                                            style={{ minWidth: 'fit-content' }}
                                        >
                                            {dataItem.label}:
                                        </Label>
                                        {dataItem.boxColor ? (
                                            <div
                                                style={{
                                                    display: 'inline-flex',
                                                    alignItems: 'center',
                                                    backgroundColor: '#daf4f0',
                                                    borderColor: '#daf4f0',
                                                    borderRadius: '5px',
                                                    color: '#0ab39c',
                                                    padding: '4px 12px',
                                                    fontSize: '12px',
                                                    fontWeight: '500',
                                                }}
                                            >
                                                {dataItem.value}
                                            </div>
                                        ) : (
                                            <span className='mb-0'>
                                                {dataItem.value}
                                            </span>
                                        )}
                                    </div>
                                ))}
                            </div>
                        </div>
                    ))}

                    {hasMoreItems && (
                        <div className='text-center mt-3'>
                            <Button
                                color='link'
                                size='sm'
                                onClick={() => setShowAll(!showAll)}
                                className='text-decoration-none'
                                style={{
                                    color: '#0ab39c',
                                    fontWeight: '500',
                                }}
                            >
                                {showAll ? (
                                    <Button>
                                        <i className='ri-arrow-up-line align-middle me-1'></i>
                                        Thu gọn
                                    </Button>
                                ) : (
                                    <Button>
                                        <i className='ri-arrow-down-line align-middle me-1'></i>
                                        Xem thêm
                                    </Button>
                                )}
                            </Button>
                        </div>
                    )}

                    {content.length === 0 && (
                        <div className='text-center text-muted py-4'>
                            <i className='ri-inbox-line fs-24 mb-2 d-block'></i>
                            Chưa có dữ liệu
                        </div>
                    )}
                </CardBody>
            </Card>
        </Col>
    );
};

export default BoxAddInfo;
